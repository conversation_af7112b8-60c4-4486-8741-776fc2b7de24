import json
import os

def extract_keyword_data(input_file, output_file, keyword):
    """
    从jsonl文件中提取包含指定关键字的数据行
    
    Args:
        input_file: 输入的jsonl文件路径
        output_file: 输出文件路径
        keyword: 要搜索的关键字
    """
    matched_lines = []
    total_lines = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                total_lines += 1
                line = line.strip()
                
                if not line:  # 跳过空行
                    continue
                
                try:
                    # 解析JSON数据
                    data = json.loads(line)
                    
                    # 将整个JSON对象转换为字符串进行搜索
                    json_str = json.dumps(data, ensure_ascii=False)
                    
                    # 检查是否包含关键字
                    if keyword in json_str:
                        matched_lines.append(line)
                        print(f"第 {line_num} 行匹配关键字: {keyword}")
                        
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    continue
    
    except FileNotFoundError:
        print(f"文件未找到: {input_file}")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 写入匹配的数据到输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for line in matched_lines:
                f.write(line + '\n')
        
        print(f"\n提取完成!")
        print(f"总共处理了 {total_lines} 行数据")
        print(f"匹配到 {len(matched_lines)} 条包含关键字 '{keyword}' 的数据")
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"写入文件时出错: {e}")

def main():
    # 文件路径配置
    input_file = "/Volumes/文枢工作空间/下载/单图V1微调评测/result.jsonl"
    output_file = "/Users/<USER>/Project/data/result2/V1单图微调/等红灯_extracted_data.jsonl"
    keyword = "类型 : 等红灯"
    
    print(f"开始提取包含关键字 '{keyword}' 的数据...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    extract_keyword_data(input_file, output_file, keyword)

if __name__ == "__main__":
    main()
